import React, { useState } from 'react';
import { Card, Typography, Input, Button, FormItem, Textarea } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { CreateDatasetSchema } from '../schemas/dataset.schema';
import { CreateDatasetDto, DatasetConversation, DatasetMessage } from '../types/dataset.types';
import { useCreateDataset } from '../hooks/useDatasetQuery';
import ChatPanel from './ChatPanel';
import { Upload } from 'lucide-react';

interface DatasetFormProps {
  /**
   * Callback khi tạo dataset thành công
   */
  onSuccess?: () => void;
}

/**
 * Component form tạo dataset
 */
const DatasetForm: React.FC<DatasetFormProps> = ({ onSuccess }) => {
  const { t } = useTranslation();
  const createDataset = useCreateDataset();

  // State cho train data và validation data
  const [trainData, setTrainData] = useState<DatasetConversation[]>([{ messages: [] }]);
  const [validationData, setValidationData] = useState<DatasetConversation[]>([{ messages: [] }]);

  // Khởi tạo form với React Hook Form và Zod
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<CreateDatasetDto>({
    resolver: zodResolver(CreateDatasetSchema),
    defaultValues: {
      name: '',
      description: '',
      trainData: [{ messages: [] }],
      validationData: [{ messages: [] }],
    },
  });

  // Xử lý thêm message vào train data
  const handleAddTrainMessage = (message: DatasetMessage) => {
    const newTrainData = [...trainData];
    newTrainData[0].messages.push(message);
    setTrainData(newTrainData);
  };

  // Xử lý xóa message khỏi train data
  const handleDeleteTrainMessage = (index: number) => {
    const newTrainData = [...trainData];
    newTrainData[0].messages.splice(index, 1);
    setTrainData(newTrainData);
  };

  // Xử lý chỉnh sửa message trong train data
  const handleEditTrainMessage = (index: number, message: DatasetMessage) => {
    const newTrainData = [...trainData];
    newTrainData[0].messages[index] = message;
    setTrainData(newTrainData);
  };

  // Xử lý thêm message vào validation data
  const handleAddValidationMessage = (message: DatasetMessage) => {
    const newValidationData = [...validationData];
    newValidationData[0].messages.push(message);
    setValidationData(newValidationData);
  };

  // Xử lý xóa message khỏi validation data
  const handleDeleteValidationMessage = (index: number) => {
    const newValidationData = [...validationData];
    newValidationData[0].messages.splice(index, 1);
    setValidationData(newValidationData);
  };

  // Xử lý chỉnh sửa message trong validation data
  const handleEditValidationMessage = (index: number, message: DatasetMessage) => {
    const newValidationData = [...validationData];
    newValidationData[0].messages[index] = message;
    setValidationData(newValidationData);
  };

  // Xử lý import file JSON/JSONL
  const handleImportFile = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = e => {
        try {
          const content = e.target?.result as string;

          // Kiểm tra file extension để xác định format
          const isJsonl = file.name.toLowerCase().endsWith('.jsonl');

          if (isJsonl) {
            // Xử lý JSONL format (mỗi dòng là 1 JSON object)
            const lines = content.trim().split('\n');
            const conversations: DatasetConversation[] = [];

            lines.forEach((line, index) => {
              try {
                const jsonObj = JSON.parse(line.trim());
                if (jsonObj.messages && Array.isArray(jsonObj.messages)) {
                  conversations.push({ messages: jsonObj.messages });
                }
              } catch (lineError) {
                console.error(`Error parsing line ${index + 1}:`, lineError);
              }
            });

            if (conversations.length > 0) {
              // Chia dataset: 80% train, 20% validation
              const splitIndex = Math.floor(conversations.length * 0.8);
              const trainConversations = conversations.slice(0, splitIndex);
              const validationConversations = conversations.slice(splitIndex);

              setTrainData(trainConversations.length > 0 ? trainConversations : [{ messages: [] }]);
              setValidationData(
                validationConversations.length > 0 ? validationConversations : [{ messages: [] }]
              );

              console.log(`Imported ${conversations.length} conversations from JSONL`);
              console.log(
                `Train: ${trainConversations.length}, Validation: ${validationConversations.length}`
              );
            }
          } else {
            // Xử lý JSON format (format cũ)
            const json = JSON.parse(content);
            if (json.trainData && json.validationData) {
              setTrainData(json.trainData);
              setValidationData(json.validationData);
            }
          }
        } catch (error) {
          console.error('Error parsing file:', error);
        }
      };
      reader.readAsText(file);
    }
  };

  // Xử lý submit form
  const onSubmit = (data: CreateDatasetDto) => {
    // Gán dữ liệu train và validation vào form data
    const formData = {
      ...data,
      trainData,
      validationData,
    };

    createDataset.mutate(formData, {
      onSuccess: () => {
        if (onSuccess) {
          onSuccess();
        }
      },
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Card className="mb-4">
        <Typography variant="h6" className="mb-4">
          {t('Dataset Fine-tuning Model')}
        </Typography>

        <div className="grid grid-cols-1 gap-4 mb-4">
          <FormItem name="name" label={t('Name')} helpText={errors.name?.message} required>
            <Input
              {...register('name')}
              placeholder={t('Nhập tên dataset')}
              error={errors.name?.message as string}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('Description')}
            helpText={errors.description?.message}
            required
          >
            <Textarea
              {...register('description')}
              placeholder={t('Nhập mô tả dataset')}
              status={errors.description?.message ? 'error' : 'default'}
              rows={4}
              fullWidth
            />
          </FormItem>
        </div>

        <div className="flex justify-end mb-4">
          <label className="btn btn-outline inline-flex items-center justify-center font-medium transition-all duration-200 px-4 py-2 cursor-pointer">
            <span className="mr-2">
              <Upload size={16} />
            </span>
            {t('Import')}
            <input type="file" accept=".json,.jsonl" hidden onChange={handleImportFile} />
          </label>
        </div>

        <ChatPanel
          title={t('Train Data')}
          messages={trainData[0].messages}
          onAddMessage={handleAddTrainMessage}
          onDeleteMessage={handleDeleteTrainMessage}
          onEditMessage={handleEditTrainMessage}
          placeholder="Nhập tin nhắn..."
        />

        <ChatPanel
          title={t('Validation Data')}
          messages={validationData[0].messages}
          onAddMessage={handleAddValidationMessage}
          onDeleteMessage={handleDeleteValidationMessage}
          onEditMessage={handleEditValidationMessage}
          placeholder="Nhập tin nhắn..."
        />

        <div className="mt-6 flex justify-end">
          <Button type="submit" isLoading={createDataset.isPending}>
            {t('Tạo Dataset')}
          </Button>
        </div>
      </Card>
    </form>
  );
};

export default DatasetForm;
