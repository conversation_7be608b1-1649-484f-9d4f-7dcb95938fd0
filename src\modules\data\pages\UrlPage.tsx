import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Card,
  Table,
  ActionMenu,
  ActionMenuItem,
  ConfirmDeleteModal,
  IconCard,
} from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import { SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { NotificationUtil } from '@/shared/utils/notification';
import { Tooltip as CustomTooltip } from '@/shared/components/common';

import {
  useUrls,
  useCreateUrl,
  useDeleteUrl,
  useDeleteMultipleUrls,
} from '@/modules/data/url/hooks/useUrlQuery';
import { UrlDto, FindAllUrlDto, CreateUrlDto, CrawlDto } from '@/modules/data/url/types/url.types';
import UrlForm from '@/modules/data/components/forms/UrlForm';
import { CrawlUrlForm } from '@/modules/data/components/forms';
import { CrawlUrlFormValues } from '@/modules/data/components/forms/CrawlUrlForm';
import useCrawlUrlWithQueue from '@/modules/data/hooks/useCrawlUrlWithQueue';
import { SortDirection } from '@/shared/dto/request/query.dto';

/**
 * Trang quản lý URL - Sử dụng DataTableWithActions
 */
const UrlPage: React.FC<Record<string, never>> = () => {
  const { t } = useTranslation(['data', 'common']);

  // State cho form và modal
  const [selectedUrl, setSelectedUrl] = useState<UrlDto | null>(null);
  const [isCrawling, setIsCrawling] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // State cho Table
  const [urls, setUrls] = useState<UrlDto[]>([]);
  const [isTableLoading, setIsTableLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // State cho xóa
  const [urlToDelete, setUrlToDelete] = useState<UrlDto | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([
    { id: 'title', label: t('data:url.table.title', 'Tiêu đề'), visible: true },
    { id: 'type', label: t('data:url.table.type', 'Loại'), visible: true },
    { id: 'tags', label: t('data:url.table.tags', 'Tags'), visible: true },
    { id: 'createdAt', label: t('data:url.table.createdAt', 'Ngày tạo'), visible: true },
  ]);

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form crawl URL
  const {
    isVisible: isCrawlFormVisible,
    showForm: showCrawlForm,
    hideForm: hideCrawlForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo<FindAllUrlDto>(() => {
    const params: FindAllUrlDto = {
      page: currentPage,
      limit: itemsPerPage,
      keyword: searchTerm || undefined,
      sortBy: sortBy || undefined,
      sortDirection: sortDirection,
    };
    return params;
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection]);

  // API hooks
  const { data: urlsData, isLoading } = useUrls(queryParams);
  const { mutateAsync: deleteUrl } = useDeleteUrl();
  const { mutateAsync: deleteMultipleUrls } = useDeleteMultipleUrls();
  const { mutateAsync: createUrl } = useCreateUrl();
  const { crawlUrlWithQueue } = useCrawlUrlWithQueue();

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (urlsData) {
      setUrls(urlsData.items);
      setTotalItems(urlsData.meta.totalItems);
    }
    setIsTableLoading(isLoading);
  }, [urlsData, isLoading]);

  // Handlers cho Table
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== itemsPerPage) {
        setItemsPerPage(newPageSize);
        setCurrentPage(1);
      }
    },
    [itemsPerPage]
  );

  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
  }, []);

  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    setSortBy(column || '');
    setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
  }, []);

  // Xử lý chỉnh sửa
  const handleEdit = useCallback(
    (url: UrlDto) => {
      setSelectedUrl(url);
      showEditForm();
    },
    [showEditForm]
  );

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((url: UrlDto) => {
    setUrlToDelete(url);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setUrlToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!urlToDelete) return;

    try {
      await deleteUrl(urlToDelete.id);
      setShowDeleteConfirm(false);
      setUrlToDelete(null);
      NotificationUtil.success({
        message: t('data:url.deleteSuccess', 'Xóa URL thành công'),
      });
    } catch (error) {
      console.error('Error deleting URL:', error);
      NotificationUtil.error({
        message: t('data:url.deleteError', 'Lỗi khi xóa URL'),
      });
    }
  }, [urlToDelete, deleteUrl, t]);

  // Xử lý xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      NotificationUtil.warning({
        message: t('data:url.selectUrlsToDelete', 'Vui lòng chọn ít nhất một URL để xóa'),
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys, t]);

  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      await deleteMultipleUrls({ ids: selectedRowKeys as string[] });
      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);
      NotificationUtil.success({
        message: t('data:url.bulkDeleteSuccess', `Đã xóa ${selectedRowKeys.length} URL thành công`),
      });
    } catch (error) {
      console.error('Error deleting URLs:', error);
      NotificationUtil.error({
        message: t('data:url.bulkDeleteError', 'Lỗi khi xóa URL'),
      });
    }
  }, [selectedRowKeys, deleteMultipleUrls, t]);

  // Định nghĩa các cột cho bảng
  const columns = useMemo<TableColumn<UrlDto>[]>(
    () => [
      {
        key: 'url',
        title: t('admin:data.url.table.url', 'URL'),
        dataIndex: 'url',
        width: '20%',
        sortable: true,
        render: (value: unknown) => (
          <CustomTooltip content={t('common.copy', 'Sao chép')}>
            <IconCard
              icon="copy"
              variant="default"
              size="sm"
              className="ml-2"
              onClick={() => {
                navigator.clipboard.writeText(String(value || ''));
              }}
            />
          </CustomTooltip>
        ),
      },
      {
        key: 'title',
        title: t('data:url.table.title', 'Tiêu đề'),
        dataIndex: 'title',
        render: (_, record: UrlDto) => (
          <div className="flex items-center">
            <div className="flex-1 min-w-0">
              <Typography variant="body2" className="font-medium truncate">
                {record.title}
              </Typography>
            </div>
          </div>
        ),
        sortable: true,
      },
      {
        key: 'type',
        title: t('data:url.table.type', 'Loại'),
        dataIndex: 'type',
        render: (value: unknown) => <span>{String(value || '-')}</span>,
        sortable: true,
      },

      {
        key: 'createdAt',
        title: t('data:url.table.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        render: (value: unknown) => {
          // Kiểm tra và chuyển đổi timestamp thành Date
          if (value) {
            const timestamp = typeof value === 'string' ? parseInt(value, 10) : (value as number);
            if (!isNaN(timestamp)) {
              return new Date(timestamp).toLocaleDateString('vi-VN');
            }
          }
          return '-';
        },
        sortable: true,
      },
      {
        key: 'actions',
        title: t('', ''),
        width: '120px',
        render: (_: unknown, record: UrlDto) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('data:url.actions.view', 'Chỉnh Sửa'),
              icon: 'eye',
              onClick: () => handleEdit(record),
            },
            {
              id: 'delete',
              label: t('common:delete', 'Xóa'),
              icon: 'trash',
              onClick: () => handleShowDeleteConfirm(record),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={true}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handleEdit, handleShowDeleteConfirm]
  );

  // Xử lý submit form thêm mới
  const handleSubmitForm = useCallback(
    async (values: Record<string, unknown>) => {
      try {
        setIsSubmitting(true);

        // Chuẩn bị dữ liệu cho API
        const urlData: CreateUrlDto = {
          url: values.url as string,
          title: values.title as string,
          content: (values.description as string) || '',
          type: values.type as string,
          tags: values.tags ? (values.tags as string).split(',').map(tag => tag.trim()) : undefined,
        };

        if (selectedUrl?.id) {
          // Cập nhật URL
          await createUrl({
            ...urlData,
            id: selectedUrl.id, // id đã được thêm vào type CreateUrlDto
          });
          NotificationUtil.success({
            message: t('data:url.updateSuccess', 'Cập nhật URL thành công'),
          });
        } else {
          // Tạo mới URL
          await createUrl(urlData);
          NotificationUtil.success({
            message: t('data:url.createSuccess', 'Tạo URL mới thành công'),
          });
        }

        hideAddForm();
        hideEditForm();
        setSelectedUrl(null);
      } catch (error) {
        console.error('Error submitting URL form:', error);
        NotificationUtil.error({
          message: t('data:url.formError', 'Lỗi khi lưu URL'),
        });
      } finally {
        setIsSubmitting(false);
      }
    },
    [selectedUrl, createUrl, hideAddForm, hideEditForm, t]
  );

  // Xử lý submit form crawl URL
  const handleSubmitCrawlUrl = useCallback(
    async (values: CrawlUrlFormValues) => {
      try {
        setIsCrawling(true);

        // Chuẩn bị dữ liệu cho API
        const crawlData: CrawlDto = {
          url: values.url,
          depth: values.depth,
          maxUrls: values.maxUrls,
          ignoreRobotsTxt: values.ignoreRobotsTxt,
        };

        // Gọi API crawl URL với TaskQueue
        await crawlUrlWithQueue(crawlData);

        // Đóng form sau khi thêm vào queue
        hideCrawlForm();
      } catch (error) {
        console.error('Error crawling URL:', error);
        NotificationUtil.error({
          message: t('data:url.crawlError', 'Lỗi khi crawl URL'),
        });
      } finally {
        setIsCrawling(false);
      }
    },
    [crawlUrlWithQueue, hideCrawlForm, t]
  );

  // Xử lý hủy form crawl URL
  const handleCancelCrawlForm = useCallback(() => {
    setIsCrawling(false);
    hideCrawlForm();
  }, [hideCrawlForm]);

  return (
    <div>
      <div className="space-y-4">
        {/* MenuIconBar */}
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={showAddForm}
          items={[
            {
              id: 'all',
              label: t('common:all', 'Tất cả'),
              icon: 'list',
              onClick: () => '',
            },
          ]}
          onColumnVisibilityChange={setVisibleColumns}
          columns={visibleColumns}
          showDateFilter={true}
          showColumnFilter={true}
          additionalIcons={[
            {
              icon: 'link',
              tooltip: t('data:url.crawl', 'Crawl URL'),
              variant: 'primary',
              onClick: showCrawlForm,
            },
            {
              icon: 'trash',
              tooltip: t('common:bulkDelete', 'Xóa nhiều'),
              variant: 'primary',
              onClick: handleShowBulkDeleteConfirm,
              className: 'text-red-500',
              condition: selectedRowKeys.length > 0,
            },
          ]}
        />

        {/* ActiveFilters */}
        <ActiveFilters
          searchTerm={searchTerm}
          onClearSearch={() => {
            setSearchTerm('');
            handleSearch('');
          }}
          sortBy={sortBy}
          sortDirection={sortDirection}
          onClearSort={() => {
            setSortBy('');
            setSortDirection(SortDirection.ASC);
            handleSortChange('', 'asc');
          }}
          onClearAll={() => {
            setSearchTerm('');
            setSortBy('');
            setSortDirection(SortDirection.ASC);
            handleSearch('');
            handleSortChange('', 'asc');
          }}
        />

        {/* SlideInForm cho form thêm mới */}
        <SlideInForm isVisible={isAddFormVisible}>
          <UrlForm onSubmit={handleSubmitForm} onCancel={hideAddForm} isSubmitting={isSubmitting} />
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {selectedUrl && (
            <UrlForm
              initialValues={selectedUrl}
              onSubmit={handleSubmitForm}
              onCancel={hideEditForm}
              isSubmitting={isSubmitting}
            />
          )}
        </SlideInForm>

        {/* SlideInForm cho form crawl URL */}
        <SlideInForm isVisible={isCrawlFormVisible}>
          <CrawlUrlForm
            onSubmit={handleSubmitCrawlUrl}
            onCancel={handleCancelCrawlForm}
            isLoading={isCrawling}
          />
        </SlideInForm>

        {/* Table */}
        <Card className="overflow-hidden">
          <Table<UrlDto>
            columns={columns}
            data={urls}
            rowKey="id"
            loading={isTableLoading}
            sortable={true}
            selectable={true}
            rowSelection={{
              selectedRowKeys,
              onChange: keys => setSelectedRowKeys(keys),
            }}
            onSortChange={handleSortChange}
            pagination={{
              current: currentPage,
              pageSize: itemsPerPage,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t('data:url.confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa URL này?')}
        itemName={urlToDelete?.title}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t(
          'data:url.confirmBulkDeleteMessage',
          `Bạn có chắc chắn muốn xóa ${selectedRowKeys.length} URL đã chọn?`
        )}
      />
    </div>
  );
};

export default UrlPage;
