import React from 'react';
import { Typo<PERSON>, Scroll<PERSON>rea, Tooltip } from '@/shared/components/common';
import { ImportedConversation } from '../types/dataset.types';
import { MessageCircle, Trash2, Upload, PanelLeftClose, PanelLeft } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface ConversationSidebarProps {
  /**
   * Danh sách conversations
   */
  conversations: ImportedConversation[];

  /**
   * Conversation hiện tại được chọn
   */
  selectedConversationId: string | null;

  /**
   * Callback khi chọn conversation
   */
  onSelectConversation: (conversationId: string) => void;

  /**
   * Callback khi xóa conversation
   */
  onDeleteConversation: (conversationId: string) => void;

  /**
   * Callback khi import file
   */
  onImportFile: (event: React.ChangeEvent<HTMLInputElement>) => void;

  /**
   * Trạng thái sidebar có mở hay không
   */
  isOpen: boolean;

  /**
   * Callback toggle sidebar
   */
  onToggleSidebar: () => void;
}

/**
 * Component sidebar hiển thị danh sách conversations
 */
const ConversationSidebar: React.FC<ConversationSidebarProps> = ({
  conversations,
  selectedConversationId,
  onSelectConversation,
  onDeleteConversation,
  onImportFile,
  isOpen,
  onToggleSidebar,
}) => {
  const { t } = useTranslation();

  // Tạo title từ first user message
  const getConversationTitle = (conversation: ImportedConversation): string => {
    const firstUserMessage = conversation.messages.find(msg => msg.role === 'user');
    if (firstUserMessage) {
      return firstUserMessage.content.length > 50 
        ? firstUserMessage.content.substring(0, 50) + '...'
        : firstUserMessage.content;
    }
    return `Conversation ${conversation.id.substring(0, 8)}`;
  };

  // Format thời gian
  const formatTime = (date: Date): string => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  return (
    <>
      {/* Toggle Button - Always visible */}
      <div className="fixed top-4 left-4 z-50">
        <Tooltip content={isOpen ? t('Đóng sidebar') : t('Mở sidebar')} position="right">
          <button
            onClick={onToggleSidebar}
            className="p-2 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            {isOpen ? (
              <PanelLeftClose size={20} className="text-gray-600 dark:text-gray-400" />
            ) : (
              <PanelLeft size={20} className="text-gray-600 dark:text-gray-400" />
            )}
          </button>
        </Tooltip>
      </div>

      {/* Sidebar */}
      <div
        className={`fixed left-0 top-0 h-full bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 transition-transform duration-300 z-40 ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
        style={{ width: '320px' }}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <Typography variant="h6" className="flex items-center mb-3">
              <MessageCircle size={20} className="mr-2 text-primary" />
              {t('Conversations')}
            </Typography>

            {/* Import Button */}
            <label className="w-full btn btn-outline inline-flex items-center justify-center font-medium transition-all duration-200 px-3 py-2 cursor-pointer text-sm">
              <Upload size={16} className="mr-2" />
              {t('Import JSONL')}
              <input
                type="file"
                accept=".jsonl,.json"
                hidden
                onChange={onImportFile}
              />
            </label>
          </div>

          {/* Conversations List */}
          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-full" invisible={true}>
              {conversations.length === 0 ? (
                <div className="p-4 text-center">
                  <div className="text-4xl mb-2">💬</div>
                  <p className="text-gray-500 dark:text-gray-400 text-sm">
                    {t('Chưa có conversation nào')}
                  </p>
                  <p className="text-gray-400 dark:text-gray-500 text-xs mt-1">
                    {t('Import file JSONL để bắt đầu')}
                  </p>
                </div>
              ) : (
                <div className="p-2">
                  {conversations.map((conversation) => (
                    <div
                      key={conversation.id}
                      className={`group relative p-3 rounded-lg cursor-pointer transition-colors mb-2 ${
                        selectedConversationId === conversation.id
                          ? 'bg-primary/10 border border-primary/20'
                          : 'hover:bg-gray-50 dark:hover:bg-gray-800 border border-transparent'
                      }`}
                      onClick={() => onSelectConversation(conversation.id)}
                    >
                      {/* Conversation Title */}
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {getConversationTitle(conversation)}
                          </h4>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {conversation.messages.length} messages • {formatTime(conversation.createdAt)}
                          </p>
                        </div>

                        {/* Delete Button */}
                        <Tooltip content={t('Xóa conversation')} position="top">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onDeleteConversation(conversation.id);
                            }}
                            className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-red-100 dark:hover:bg-red-900/20 text-red-500 transition-opacity"
                          >
                            <Trash2 size={14} />
                          </button>
                        </Tooltip>
                      </div>

                      {/* Preview Messages */}
                      <div className="mt-2 space-y-1">
                        {conversation.messages.slice(0, 2).map((message, index) => (
                          <div key={index} className="text-xs">
                            <span className={`font-medium ${
                              message.role === 'user' ? 'text-blue-600 dark:text-blue-400' :
                              message.role === 'assistant' ? 'text-green-600 dark:text-green-400' :
                              'text-gray-600 dark:text-gray-400'
                            }`}>
                              {message.role}:
                            </span>
                            <span className="text-gray-600 dark:text-gray-300 ml-1">
                              {message.content.length > 60 
                                ? message.content.substring(0, 60) + '...'
                                : message.content
                              }
                            </span>
                          </div>
                        ))}
                        {conversation.messages.length > 2 && (
                          <div className="text-xs text-gray-400 dark:text-gray-500">
                            +{conversation.messages.length - 2} more messages
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </div>
        </div>
      </div>

      {/* Overlay khi sidebar mở trên mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={onToggleSidebar}
        />
      )}
    </>
  );
};

export default ConversationSidebar;
