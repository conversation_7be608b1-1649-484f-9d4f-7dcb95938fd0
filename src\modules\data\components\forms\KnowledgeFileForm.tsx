import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Typo<PERSON>, Button } from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';

// Hàm formatBytes
const formatBytes = (bytes: number, decimals = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

interface KnowledgeFileFormProps {
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

/**
 * Form tạo file tri thức
 */
const KnowledgeFileForm: React.FC<KnowledgeFileFormProps> = ({ onSubmit, onCancel, isLoading }) => {
  const { t } = useTranslation(['data']);
  const [files, setFiles] = useState<File[]>([]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFiles(Array.from(e.target.files));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (files.length === 0) {
      NotificationUtil.info({
        message: t(
          'data:knowledgeFiles.selectFilesToUpload',
          'Please select at least one file to upload'
        ),
        duration: 3000,
      });
      return;
    }

    // Create an array of file data
    const fileData = files.map(file => ({
      name: file.name,
      mime: file.type,
      storage: file.size > 0 ? file.size : 1, // Ensure storage is never 0
    }));

    // Pass the array of files to the onSubmit handler
    onSubmit({ files: fileData });
  };

  const removeFile = (index: number) => {
    setFiles(files.filter((_, i) => i !== index));
  };

  return (
    <div className="p-6 bg-white dark:bg-gray-800">
      <form onSubmit={handleSubmit} className="space-y-6">
        <Typography variant="h5" className="mb-4 dark:text-primary-400">
          {t('data:knowledgeFiles.uploadFiles', 'Upload Knowledge Files')}
        </Typography>

        <div className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center transition-colors">
            <label className="block cursor-pointer">
              <div className="flex flex-col items-center justify-center space-y-2">
                <svg
                  className="w-12 h-12 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                  />
                </svg>
                <Typography variant="body1" className="font-medium">
                  {t(
                    'data:knowledgeFiles.dragAndDrop',
                    'Drag and drop files here, or click to select files'
                  )}
                </Typography>
                <Typography variant="caption" className="text-gray-500 dark:text-gray-400">
                  {t(
                    'data:knowledgeFiles.supportedFormats',
                    'Supported formats: PDF, DOCX, TXT, CSV, JSON'
                  )}
                </Typography>
              </div>
              <input
                type="file"
                multiple
                onChange={handleFileChange}
                className="hidden"
                disabled={isLoading}
                accept=".pdf,.docx,.txt,.csv,.json,.doc,.xlsx,.xls"
              />
            </label>
          </div>

          {files.length > 0 && (
            <div className="mt-4">
              <Typography variant="body2" className="font-medium mb-2">
                {t('data:knowledgeFiles.selectedFiles', 'Selected Files')} ({files.length})
              </Typography>
              <div className="space-y-2 max-h-60 overflow-y-auto p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                {files.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600"
                  >
                    <div className="flex items-center space-x-3">
                      <svg
                        className="w-6 h-6 text-gray-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      <div className="truncate max-w-xs">
                        <Typography variant="body2" className="font-medium truncate">
                          {file.name}
                        </Typography>
                        <Typography variant="caption" className="text-gray-500">
                          {formatBytes(file.size)}
                        </Typography>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={e => {
                        e.preventDefault();
                        removeFile(index);
                      }}
                      className="text-red-500 hover:text-red-700"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700 mt-6">
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            {t('common:cancel', 'Cancel')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isLoading || files.length === 0}
            isLoading={isLoading}
          >
            {isLoading
              ? t('data.common.uploading', 'Uploading...')
              : t('data.common.upload', 'Upload')}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default KnowledgeFileForm;
